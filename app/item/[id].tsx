import { Button, Container } from "@/components";
import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Item } from "@/types";
import { StyleSheetCreate, getItemById } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { router, useFocusEffect, useLocalSearchParams } from "expo-router";
import React, { useCallback, useRef, useState } from "react";
import { Dimensions, Pressable, ScrollView, Text, View } from "react-native";
import { Gesture, GestureDetector, GestureHandlerRootView } from "react-native-gesture-handler";
import Animated, { runOnJS, useAnimatedStyle, useSharedValue, withSpring } from "react-native-reanimated";

export default function ItemDetailsScreen() {
  const { id } = useLocalSearchParams();
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  // Get actual screen dimensions
  const { width: screenWidth } = Dimensions.get('window');

  // State for real item data
  const [item, setItem] = useState<Item | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showFullscreenImage, setShowFullscreenImage] = useState(false);
  const [isDetailExpanded, setIsDetailExpanded] = useState(false);

  // Refs for scroll views
  const imageScrollRef = useRef<ScrollView>(null);

  // Animated values for swipe gesture
  const translateY = useSharedValue(0);

  // Load item data on component mount
  useFocusEffect(
    useCallback(() => {
      loadItem();
    }, [id])
  );

  const loadItem = async () => {
    if (!id || typeof id !== "string") {
      setError("Invalid item ID");
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      const response = await getItemById(id);

      if (response.success && response.data) {
        setItem(response.data);
      } else {
        setError("Item not found");
      }
    } catch (error) {
      console.error("Error loading item:", error);
      setError("Failed to load item");
    } finally {
      setIsLoading(false);
    }
  };



  // Remove edit mode state since we navigate to creation page

  const handleBack = () => {
    router.back();
  };

  const handleImagePress = (index: number) => {
    setCurrentImageIndex(index);
    setShowFullscreenImage(true);
  };

  // Navigation function for image slider
  const navigateToImage = (direction: 'prev' | 'next' | number) => {
    if (!item?.images) return;

    let newIndex: number;

    if (typeof direction === 'number') {
      newIndex = direction;
    } else if (direction === 'prev') {
      newIndex = currentImageIndex > 0 ? currentImageIndex - 1 : item.images.length - 1;
    } else {
      newIndex = currentImageIndex < item.images.length - 1 ? currentImageIndex + 1 : 0;
    }

    setCurrentImageIndex(newIndex);

    // Scroll to the new image
    if (imageScrollRef.current) {
      imageScrollRef.current.scrollTo({
        x: newIndex * screenWidth,
        animated: true,
      });
    }
  };

  const handleSeparationPress = () => {
    setIsDetailExpanded(!isDetailExpanded);
  };

  // Swipe gesture for expanding/collapsing details
  const panGesture = Gesture.Pan()
    .onUpdate((event) => {
      if (event.translationY < 0) {
        // Swiping up
        translateY.value = Math.max(event.translationY, -100);
      } else {
        // Swiping down
        translateY.value = Math.min(event.translationY, 100);
      }
    })
    .onEnd((event) => {
      const threshold = 50;
      if (event.translationY < -threshold && !isDetailExpanded) {
        // Swipe up to expand
        runOnJS(setIsDetailExpanded)(true);
      } else if (event.translationY > threshold && isDetailExpanded) {
        // Swipe down to collapse
        runOnJS(setIsDetailExpanded)(false);
      }
      translateY.value = withSpring(0);
    });

  // Animated style for the detail section
  const detailAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  // Gesture for horizontal image swiping
  const imageSwipeGesture = Gesture.Pan()
    .onEnd((event) => {
      const threshold = 50;
      const velocity = event.velocityX;

      // Only respond to horizontal swipes (more horizontal than vertical)
      if (Math.abs(event.translationX) > Math.abs(event.translationY)) {
        if (Math.abs(event.translationX) > threshold || Math.abs(velocity) > 500) {
          if (event.translationX > 0 || velocity > 0) {
            // Swipe right - go to previous image
            runOnJS(navigateToImage)('prev');
          } else {
            // Swipe left - go to next image
            runOnJS(navigateToImage)('next');
          }
        }
      }
    })
    .minDistance(20);

  const handleEdit = () => {
    if (!item) return;

    // Navigate to item creation page with pre-filled data
    router.push({
      pathname: "/item/create",
      params: { id: item.id },
    });
  };



  // Show loading state
  if (isLoading) {
    return (
      <Container
        style={{ flex: 1, backgroundColor: colors.backgroundApp }}
        headerProps={{
          title: "故事详情",
        }}
      >
        <View style={[styles.content, styles.centerContent]}>
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>加载中...</Text>
        </View>
      </Container>
    );
  }

  // Show error state
  if (error || !item) {
    return (
      <Container
        style={{ flex: 1, backgroundColor: colors.backgroundApp }}
        headerProps={{
          title: "故事详情",
        }}
      >
        <View style={[styles.content, styles.centerContent]}>
          <Text style={[styles.errorText, { color: colors.error }]}>{error || "物品未找到"}</Text>
          <Button title="返回" onPress={handleBack} style={styles.backButton} />
        </View>
      </Container>
    );
  }

console.log(item, 'item')

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={styles.container}>
        {/* Hero Image Section */}
        <GestureDetector gesture={imageSwipeGesture}>
          <View style={[
            styles.heroSection,
            { height: isDetailExpanded ? '40%' : '60%' }
          ]}>
            {item.images && item.images.length > 0 ? (
            <ScrollView
              ref={imageScrollRef}
              horizontal
              pagingEnabled
              showsHorizontalScrollIndicator={false}
              onMomentumScrollEnd={event => {
                const index = Math.round(
                  event.nativeEvent.contentOffset.x / screenWidth
                );
                setCurrentImageIndex(index);
              }}
              onScrollEndDrag={event => {
                const index = Math.round(
                  event.nativeEvent.contentOffset.x / screenWidth
                );
                setCurrentImageIndex(index);
              }}
              onScroll={event => {
                // Optional: Add smooth indicator animation based on scroll position
                const scrollX = event.nativeEvent.contentOffset.x;
                const index = Math.round(scrollX / screenWidth);
                if (index !== currentImageIndex) {
                  setCurrentImageIndex(index);
                }
              }}
              style={styles.heroImageScroll}
              contentContainerStyle={{
                width: item.images.length * screenWidth,
                alignItems: 'center'
              }}
              decelerationRate="fast"
              snapToInterval={screenWidth}
              snapToAlignment="center"
              bounces={false}
              scrollEventThrottle={16}
              directionalLockEnabled={true}
              alwaysBounceHorizontal={false}
              alwaysBounceVertical={false}
            >
              {item.images.map((imageUrl, index) => (
                <Pressable
                  key={index}
                  style={[styles.heroImageSlide, { width: screenWidth }]}
                  onPress={() => handleImagePress(index)}
                >
                  <Image
                    source={{ uri: imageUrl }}
                    style={styles.heroImage}
                    contentFit="cover"
                    transition={200}
                  />
                </Pressable>
              ))}
            </ScrollView>
          ) : (
            <View style={styles.heroPlaceholder}>
              <Ionicons name="image-outline" size={80} color="#666" />
            </View>
          )}

          {/* Hero Overlay */}
          <View style={styles.heroOverlay} />



          {/* Enhanced Image Indicators */}
          {item.images && item.images.length > 1 && (
            <View style={styles.imageIndicators}>
              {item.images.map((_, index) => (
                <Pressable
                  key={index}
                  style={[
                    styles.indicator,
                    {
                      backgroundColor:
                        index === currentImageIndex ? "white" : "rgba(255,255,255,0.4)",
                      transform: [{ scale: index === currentImageIndex ? 1.3 : 1 }],
                      width: index === currentImageIndex ? 12 : 8,
                      height: index === currentImageIndex ? 12 : 8,
                      borderRadius: index === currentImageIndex ? 6 : 4,
                    },
                  ]}
                  onPress={() => navigateToImage(index)}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                />
              ))}
            </View>
          )}

          {/* Hero Controls */}
          <View style={styles.heroControls}>
            <Pressable style={styles.actionBtn} onPress={handleBack}>
              <Ionicons name="arrow-back" size={18} color="#000" />
            </Pressable>

            <View style={styles.actionButtons}>
              <Pressable style={styles.actionBtn} onPress={handleEdit}>
                <Ionicons name="create" size={18} color="#000" />
              </Pressable>
            </View>
          </View>
        </View>
        </GestureDetector>

        {/* Separation Element with Gesture */}
        <GestureDetector gesture={panGesture}>
          <Animated.View style={[detailAnimatedStyle]}>
            <Pressable
              style={styles.separationElement}
              onPress={handleSeparationPress}
            >
              <View style={styles.separationHandle} />
              <Text style={styles.separationHint}>
                {isDetailExpanded ? '向下滑动收起' : '向上 1滑动展开'}
              </Text>
            </Pressable>

            {/* Detail Section */}
            <ScrollView
              style={[
                styles.detailSection,
                { height: isDetailExpanded ? '60%' : '40%' }
              ]}
              showsVerticalScrollIndicator={false}
            >
          <View style={styles.detailContent}>
            {/* Artwork Title */}
            <Text style={styles.artworkTitle}>{item.name}</Text>

            {/* Artwork Specs */}
            <Text style={styles.artworkSpecs}>
              {item.timeOfPossession && item.currentLocation
                ? `${item.timeOfPossession} • ${item.currentLocation}`
                : item.timeOfPossession || item.currentLocation || "故事详情"}
            </Text>

            {/* Tags Section */}
            {item.tags && item.tags.length > 0 && (
              <View style={styles.tagsSection}>
                {item.tags.map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{tag}</Text>
                  </View>
                ))}
              </View>
            )}

            {/* Dividing Line */}
            {item.tags && item.tags.length > 0 && <View style={styles.dividingLine} />}

            {/* Artwork Description */}
            <View style={styles.artworkDescription}>
              <Text style={styles.descriptionText}>
                {item.content ||
                  "这是一个珍贵的故事，记录了生活中的美好瞬间。每一个细节都承载着特殊的意义，值得我们细细品味和回忆。"}
              </Text>
            </View>
          </View>
        </ScrollView>
          </Animated.View>
        </GestureDetector>
      </View>

      {/* Fullscreen Image Modal */}
      {React.createElement(require("@/components/FullscreenImageModal").default, {
        visible: showFullscreenImage,
        images: item?.images || [],
        currentIndex: currentImageIndex,
        onClose: () => setShowFullscreenImage(false),
        onIndexChange: setCurrentImageIndex,
      })}
    </GestureHandlerRootView>
  );
}

const styles = StyleSheetCreate({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  // Hero Section
  heroSection: {
    position: "relative",
    height: "60%", // 60vh equivalent
    overflow: "hidden",
  },
  heroImage: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
    backgroundColor: "#1a1a1a", // Fallback background while loading
  },
  heroImageScroll: {
    width: "100%",
    height: "100%",
  },
  heroImageSlide: {
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  imageIndicators: {
    position: "absolute",
    bottom: 20,
    left: 0,
    right: 0,
    flexDirection: "row",
    justifyContent: "center",
    gap: 8,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  // Separation Element
  separationElement: {
    backgroundColor: "#000",
    paddingVertical: 16,
    alignItems: "center",
    justifyContent: "center",
    borderTopWidth: 1,
    borderTopColor: "#1a1a1a",
  },
  separationHandle: {
    width: 50,
    height: 5,
    backgroundColor: "#444",
    borderRadius: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 2,
    marginBottom: 8,
  },
  separationHint: {
    fontSize: 12,
    color: "#666",
    fontWeight: "400",
  },
  heroPlaceholder: {
    width: "100%",
    height: "100%",
    backgroundColor: "#333",
    alignItems: "center",
    justifyContent: "center",
  },
  heroOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.3)",
  },
  heroControls: {
    position: "absolute",
    top: 60, // Account for status bar
    left: 20,
    right: 20,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  actionButtons: {
    flexDirection: "row",
    gap: 16,
  },
  actionBtn: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    alignItems: "center",
    justifyContent: "center",
  },
  // Detail Section
  detailSection: {
    flex: 1,
    backgroundColor: "#000",
    paddingTop: 20,
  },
  detailContent: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  // Artwork Title (Serif font style)
  artworkTitle: {
    fontSize: 32,
    fontWeight: "400",
    color: "#fff",
    marginBottom: 8,
    fontFamily: "serif", // This will use system serif font
    lineHeight: 38,
  },
  // Artwork Specs
  artworkSpecs: {
    fontSize: 14,
    color: "#999",
    marginBottom: 24,
    fontWeight: "400",
  },
  // Tags Section
  tagsSection: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
    marginBottom: 20,
  },
  tag: {
    backgroundColor: "#333",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "#555",
  },
  tagText: {
    fontSize: 12,
    color: "#ccc",
    fontWeight: "500",
  },
  // Dividing Line
  dividingLine: {
    height: 1,
    backgroundColor: "#333",
    marginVertical: 20,
    opacity: 0.6,
  },
  // Tags Inline
  tagsInline: {
    flexDirection: "row",
    marginBottom: 20,
    flexWrap: "wrap",
  },
  tagsPrefix: {
    fontSize: 14,
    color: "#999",
    fontWeight: "400",
  },
  tagsText: {
    fontSize: 14,
    color: "#ccc",
    fontWeight: "400",
  },
  // Artwork Description
  artworkDescription: {
    marginBottom: 20,
  },
  descriptionText: {
    fontSize: 16,
    color: "#ccc",
    lineHeight: 24,
    fontWeight: "400",
  },

  // Time and Location Section
  timeLocationSection: {
    flexDirection: "row",
    alignItems: "center",
    flexWrap: "wrap",
    gap: 12,
    marginBottom: 18,
    paddingVertical: 8,
  },
  timeInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    backgroundColor: "#f0f9ff",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "#bae6fd",
    shadowColor: "rgba(88, 174, 229, 0.1)",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 1,
  },
  timeIcon: {
    fontSize: 14,
  },
  timeText: {
    fontSize: 13,
    fontWeight: "600",
    color: "#0369a1",
  },
  locationInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    backgroundColor: "#f0fdf4",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "#bbf7d0",
    shadowColor: "rgba(16, 185, 129, 0.1)",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 1,
  },
  locationIcon: {
    fontSize: 14,
  },
  locationText: {
    fontSize: 13,
    fontWeight: "600",
    color: "#059669",
  },

  // Story Section
  storySection: {
    marginBottom: 20,
  },
  storyTitle: {
    fontSize: 17,
    fontWeight: "700",
    color: "#1a365d",
    marginBottom: 12,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  storyContent: {
    backgroundColor: "#f8fafc",
    borderRadius: 20,
    padding: 20,
    borderWidth: 1,
    borderColor: "rgba(88, 174, 229, 0.1)",
    shadowColor: "rgba(88, 174, 229, 0.05)",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 1,
    shadowRadius: 15,
    elevation: 2,
    borderLeftWidth: 4,
    borderLeftColor: "#58aee5",
  },
  storyText: {
    fontSize: 15,
    lineHeight: 24,
    color: "#4a5568",
    textAlign: "justify",
  },
  // Legacy styles for compatibility
  content: {
    flex: 1,
  },
  centerContent: {
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 64,
  },
  loadingText: {
    fontSize: 16,
    textAlign: "center",
  },
  errorText: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 20,
  },
  backButton: {
    alignSelf: "center",
  },
});
